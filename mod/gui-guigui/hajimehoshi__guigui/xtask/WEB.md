# web

The main way that users interact with the system.

We use DataStar for the web interface. https://github.com/starfederation/datastar

For Components we use https://github.com/CoreyCole/datastarui, which uses templ for Templating. 

Demo at: https://datastar-ui.com

## WASM in the Browser 

https://github.com/wanixdev/wanix.sh and https://github.com/tractordev/toolkit-go

Allow running WASM in the Browser, so that we can have golang running in the browser, so we can do rendering from this layer, and so have an optimistic rendering pipeline. 

https://github.com/cookiengineer/gooey is also interesting.

Testing using https://github.com/playwright-community/playwright-go ?













