<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>xtask Dashboard</title>
    <script type="module" src="https://cdn.jsdelivr.net/npm/@starfederation/datastar@1.0.0-beta.11/dist/datastar.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: #2563eb;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .header p {
            opacity: 0.9;
            margin-top: 0.25rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .card h2 {
            font-size: 1.25rem;
            margin-bottom: 1rem;
            color: #1f2937;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }
        
        .status-dot.offline {
            background: #ef4444;
        }
        
        .button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .button:hover {
            background: #1d4ed8;
        }
        
        .button.secondary {
            background: #6b7280;
        }
        
        .button.secondary:hover {
            background: #4b5563;
        }
        
        .input-group {
            margin-bottom: 1rem;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 0.25rem;
            font-weight: 500;
        }
        
        .input-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        
        .output {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.75rem;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 1rem;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .tool-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 1rem;
            text-align: center;
        }
        
        .tool-card h3 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .tool-card p {
            font-size: 0.75rem;
            color: #64748b;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 xtask Dashboard</h1>
        <p>Cross-platform development task runner with embedded NATS coordination</p>
        <p><small>Real-time updates via HTTP API</small></p>
    </div>
    
    <div class="container">
        <div class="grid">
            <!-- System Status -->
            <div class="card" data-store="{daemonStatus: 'online', natsStatus: 'online', apiStatus: 'online'}">
                <h2>📊 System Status</h2>
                <div class="status">
                    <div class="status-dot" data-class="status-dot" data-class.offline="daemonStatus === 'offline'"></div>
                    <span>xtask Daemon</span>
                    <span data-text="daemonStatus" style="margin-left: 0.5rem; font-size: 0.75rem; opacity: 0.7;"></span>
                </div>
                <div class="status">
                    <div class="status-dot" data-class="status-dot" data-class.offline="natsStatus === 'offline'"></div>
                    <span>NATS JetStream</span>
                    <span data-text="natsStatus" style="margin-left: 0.5rem; font-size: 0.75rem; opacity: 0.7;"></span>
                </div>
                <div class="status">
                    <div class="status-dot" data-class="status-dot" data-class.offline="apiStatus === 'offline'"></div>
                    <span>HTTP API</span>
                    <span data-text="apiStatus" style="margin-left: 0.5rem; font-size: 0.75rem; opacity: 0.7;"></span>
                </div>
                <button class="button" data-on-click="$$get('/health')">Refresh Status</button>
            </div>
            
            <!-- Quick Tools -->
            <div class="card" data-store="{whichBinary: 'go', downloadUrl: '', output: 'Welcome to xtask dashboard! 🎉\n\nTry the tools above or check the system status.\n'}">
                <h2>🔧 Quick Tools</h2>
                <div class="input-group">
                    <label>Binary to find:</label>
                    <input type="text" data-model="whichBinary" placeholder="e.g., go, node, python">
                </div>
                <button class="button"
                        data-on-click="$get('/api/v1/tools/which/' + whichBinary).then(r => r.json()).then(data => {
                            const timestamp = new Date().toLocaleTimeString();
                            if(data.found) {
                                output += `\n[${timestamp}] ✅ ${whichBinary} found at: ${data.path}`;
                            } else {
                                output += `\n[${timestamp}] ❌ ${whichBinary} not found`;
                            }
                        }).catch(err => {
                            output += `\n[${new Date().toLocaleTimeString()}] ❌ Error: ${err.message}`;
                        })">
                    Find Binary
                </button>

                <div class="input-group" style="margin-top: 1rem;">
                    <label>URL to download:</label>
                    <input type="text" data-model="downloadUrl" placeholder="https://example.com/file.zip">
                </div>
                <button class="button"
                        data-on-click="$post('/api/v1/tools/got', {url: downloadUrl}).then(r => r.json()).then(data => {
                            const timestamp = new Date().toLocaleTimeString();
                            if(data.success) {
                                output += `\n[${timestamp}] ✅ Downloaded: ${downloadUrl}`;
                            } else {
                                output += `\n[${timestamp}] ❌ Download failed: ${data.error || 'Unknown error'}`;
                            }
                        }).catch(err => {
                            output += `\n[${new Date().toLocaleTimeString()}] ❌ Error: ${err.message}`;
                        })">
                    Download File
                </button>
            </div>
            
            <!-- Command Executor -->
            <div class="card" data-store="{command: '', args: ''}">
                <h2>⚡ Command Executor</h2>
                <div class="input-group">
                    <label>Command:</label>
                    <input type="text" data-model="command" placeholder="e.g., which, got, silent">
                </div>
                <div class="input-group">
                    <label>Arguments:</label>
                    <input type="text" data-model="args" placeholder="command arguments">
                </div>
                <button class="button"
                        data-on-click="$post('/api/v1/tasks', {command: command, args: args.split(' ').filter(a => a)}).then(r => r.json()).then(data => {
                            const timestamp = new Date().toLocaleTimeString();
                            if(data.success) {
                                output += `\n[${timestamp}] ✅ Command executed: ${command} ${args}`;
                                if(data.output) {
                                    output += `\n${data.output}`;
                                }
                            } else {
                                output += `\n[${timestamp}] ❌ Command failed: ${data.error || 'Unknown error'}`;
                            }
                        }).catch(err => {
                            output += `\n[${new Date().toLocaleTimeString()}] ❌ Error: ${err.message}`;
                        })">
                    Execute
                </button>
                <button class="button secondary" data-on-click="output = 'Output cleared.\n'">Clear</button>
            </div>
        </div>
        
        <!-- Available Tools -->
        <div class="card">
            <h2>🛠️ Available Tools</h2>
            <div class="tools-grid">
                <div class="tool-card">
                    <h3>which</h3>
                    <p>Find binary location</p>
                    <button class="button"
                            data-on-click="$get('/api/v1/tools/which/go').then(r => r.json()).then(data => {
                                const timestamp = new Date().toLocaleTimeString();
                                if(data.found) {
                                    output += `\n[${timestamp}] ✅ go found at: ${data.path}`;
                                } else {
                                    output += `\n[${timestamp}] ❌ go not found`;
                                }
                            })">
                        Test
                    </button>
                </div>
                <div class="tool-card">
                    <h3>got</h3>
                    <p>Download files</p>
                    <button class="button"
                            data-on-click="$post('/api/v1/tools/got', {url: 'https://httpbin.org/json'}).then(r => r.json()).then(data => {
                                const timestamp = new Date().toLocaleTimeString();
                                if(data.success) {
                                    output += `\n[${timestamp}] ✅ Downloaded test file`;
                                } else {
                                    output += `\n[${timestamp}] ❌ Download failed: ${data.error}`;
                                }
                            })">
                        Test
                    </button>
                </div>
                <div class="tool-card">
                    <h3>health-check</h3>
                    <p>HTTP health checks</p>
                    <button class="button"
                            data-on-click="$post('/api/v1/tools/health-check', {url: 'https://httpbin.org/status/200'}).then(r => r.json()).then(data => {
                                const timestamp = new Date().toLocaleTimeString();
                                if(data.success) {
                                    output += `\n[${timestamp}] ✅ Health check passed`;
                                } else {
                                    output += `\n[${timestamp}] ❌ Health check failed`;
                                }
                            })">
                        Test
                    </button>
                </div>
                <div class="tool-card">
                    <h3>tree</h3>
                    <p>Directory tree</p>
                    <button class="button"
                            data-on-click="$get('/api/v1/tools/tree?path=.').then(r => r.json()).then(data => {
                                const timestamp = new Date().toLocaleTimeString();
                                output += `\n[${timestamp}] 📁 Directory tree generated`;
                            })">
                        Test
                    </button>
                </div>
                <div class="tool-card">
                    <h3>kill-port</h3>
                    <p>Kill process on port</p>
                    <button class="button" data-on-click="command = 'kill-port'; args = '8080'">Set Example</button>
                </div>
                <div class="tool-card">
                    <h3>wait-for-port</h3>
                    <p>Wait for port</p>
                    <button class="button" data-on-click="command = 'wait-for-port'; args = '8080 30s'">Set Example</button>
                </div>
            </div>
        </div>
        
        <!-- Output -->
        <div class="card">
            <h2>📋 Output</h2>
            <div class="output" data-text="output" id="output-display">Welcome to xtask dashboard! 🎉

Try the tools above or check the system status.
Real-time updates via DataStar + NATS SSE
            </div>
        </div>
    </div>
    
    <script>
        // DataStar event handlers for real-time updates
        document.addEventListener('datastar-event', (event) => {
            const { type, data } = event.detail;

            // Handle different event types from NATS
            switch (type) {
                case 'command_result':
                    // Update output with command results
                    const timestamp = new Date().toLocaleTimeString();
                    const currentOutput = document.querySelector('[data-text="output"]').textContent;
                    const newOutput = currentOutput + `\n[${timestamp}] ${data.output || data.error}`;
                    document.querySelector('[data-store]').dataset.store = JSON.stringify({
                        ...JSON.parse(document.querySelector('[data-store]').dataset.store),
                        output: newOutput
                    });
                    break;

                case 'status_update':
                    // Update system status
                    document.querySelector('[data-store]').dataset.store = JSON.stringify({
                        ...JSON.parse(document.querySelector('[data-store]').dataset.store),
                        daemonStatus: data.daemon || 'online',
                        natsStatus: data.nats || 'online',
                        apiStatus: data.api || 'online'
                    });
                    break;
            }
        });

        // Initialize SSE connection for real-time updates
        console.log('🚀 xtask Dashboard with DataStar + NATS SSE initialized');
    </script>
</body>
</html>
