# Markdown

Users write in markdown, and so using HUGO, see all their content.

Render to PDF with TOC:
https://github.com/solworktech/md2pdf/issues/73#issuecomment-2957228232

DeckSH can reach into the markdown, and reuse the markdown text, and so via Deck redner to SVG, PNG and PDF.

## Translations

https://github.com/romshark/toki/ can reach into the golang to create translations.

I think that we can build a parser that parses the markdown, to create translations too.

We do not have a way to Translate the bundles .Not sure what is envisaged to do this. An Ollama AI could do it.

Once the bundles are translated, we can use them at serve golang or markdown that is translated. 


### UOM

https://github.com/romshark/tik/issues/3 is in progress to allow use to translate units of measurement too.



