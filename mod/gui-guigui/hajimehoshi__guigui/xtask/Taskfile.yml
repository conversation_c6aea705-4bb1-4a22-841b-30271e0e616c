# https://taskfile.dev

version: '3'

vars:
  APP_NAME: xtask
  BIN_DIR: .bin
  CMD_DIR: ./cmd/xtask

tasks:
  default:
    desc: "Show available tasks"
    cmds:
      - task --list-all
    silent: true

  build:
    desc: "Build xtask binary"
    cmds:
      - mkdir -p {{.BIN_DIR}}
      - go build -o {{.BIN_DIR}}/{{.APP_NAME}} {{.CMD_DIR}}

  clean:
    desc: "Clean build artifacts"
    cmds:
      - rm -rf {{.BIN_DIR}}

  clean:data:
    desc: "Clean daemon data"
    cmds:
      - rm -rf .data

  run:help:
    desc: "Show xtask help"
    deps: [build]
    cmds:
      - ./{{.BIN_DIR}}/{{.APP_NAME}} -h

  run:daemon:
    desc: "Start xtask daemon"
    deps: [build]
    cmds:
      - ./{{.BIN_DIR}}/{{.APP_NAME}} daemon --verbose

  run:daemon:bg:
    desc: "Start xtask daemon in background"
    deps: [build]
    cmds:
      - nohup ./{{.BIN_DIR}}/{{.APP_NAME}} daemon --port 8080 --nats-port 4223 --data-dir ./.data > xtask.log 2>&1 &

  run:daemon:stop:
    desc: "Stop background xtask daemon"
    cmds:
      - pkill -f 'xtask daemon' || true

  run:tree:
    desc: "Show directory tree"
    deps: [build]
    cmds:
      - ./{{.BIN_DIR}}/{{.APP_NAME}} tree .

  run:which:
    desc: "Find binary location"
    deps: [build]
    cmds:
      - ./{{.BIN_DIR}}/{{.APP_NAME}} which {{.CLI_ARGS | default "go"}}

  test:
    desc: "Run tests"
    cmds:
      - go test -v ./...

  kill:daemon:
    desc: "Kill xtask daemon processes"
    cmds:
      - pkill -f 'xtask daemon' || true
  

