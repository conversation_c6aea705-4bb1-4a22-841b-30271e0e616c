# Internationalization (i18n) Strategy for xtask

**Multi-language support for xtask GUI components and documentation**

## 🎯 Requirements Summary

Based on your notes, xtask needs comprehensive i18n support for:

### Core Needs
- **Multi-language GUI support** for global development teams
- **Consistent i18n** across different UI frameworks
- **Machine translation integration** for automated localization
- **Markdown translation** for documentation and presentations

### Target Frameworks
- **Ebiten GUIGUI** - Native desktop applications. https://github.com/hajimehoshi/guigui
- **Web DataStar GUI** - Web-based interfaces. https://github.com/starfederation/datastar
- **Deck SH** - Presentation system with markdown integration. https://github.com/ajstarks/decksh

## 🔧 Technical Stack

### Primary i18n Library: Toki
- **Repository**: https://github.com/romshark/toki
- **Issue reference**: https://github.com/romshark/toki/issues/8#event-18046115563
- **Go-native** - Perfect for xtask's all-Go architecture
- **Bundle generation** - Compile-time string extraction
- **Machine translation challenge** - How to translate toki bundles automatically

## 🌐 Unified Architecture Strategy

### Cross-Framework i18n
```go
// Shared i18n for both Ebiten and DataStar
package i18n

import "github.com/romshark/toki"

type XtaskLocalizer struct {
    toki *toki.Localizer
    fallback string
}

// Works for both GUI frameworks
func (x *XtaskLocalizer) T(key string, args ...interface{}) string {
    return x.toki.Localize(context.Background(), key, args...)
}
```

### Machine Translation Pipeline
```yaml
# Automated translation workflow
translation_pipeline:
  1. Extract strings with toki
  2. Generate base bundles (English)
  3. Machine translate to target languages
  4. Human review and refinement
  5. Compile into xtask binary
```

### Markdown Translation System
```go
// For Deck SH integration
type TranslatedMarkdown struct {
    Original   string
    Translated map[string]string // lang -> content
    Metadata   map[string]interface{}
}

func (deck *DeckSH) GetSlideText(slideID, lang string) string {
    markdown := deck.GetMarkdown(slideID)
    if content, exists := markdown.Translated[lang]; exists {
        return content
    }
    return markdown.Original // Fallback
}
```

## 🚀 Implementation Benefits

### For xtask Ecosystem
- **Global team support** - Native language interfaces
- **Consistent terminology** across all components
- **Go-native approach** - No external runtime dependencies
- **Automated workflows** - Machine translation pipeline

### Technical Advantages
- **Compile-time** - Translations embedded in binary
- **Type-safe** - Compile-time string validation
- **Performance** - No runtime translation overhead
- **Cross-platform** - Works everywhere xtask works

## 🎯 Key Challenges to Solve

### 1. Toki Bundle Translation
**Challenge**: How to machine translate toki-generated bundles while preserving formatting and placeholders

**Solution**: Custom translation pipeline that understands toki syntax

### 2. Cross-Framework Consistency
**Challenge**: Ensuring same translations work for both Ebiten and DataStar

**Solution**: Shared translation keys and unified localizer interface

### 3. Markdown Integration
**Challenge**: Translating markdown content for Deck SH presentations

**Solution**: Structured markdown with embedded translation metadata

## 🌟 Strategic Value

This i18n strategy transforms xtask from an English-only tool into a **globally accessible development platform** that can serve international teams in their native languages, while maintaining the all-Go architecture philosophy.

**The combination of toki for Go-native i18n, automated machine translation, and markdown localization creates a comprehensive internationalization platform that scales with xtask's global ambitions!** 🌍